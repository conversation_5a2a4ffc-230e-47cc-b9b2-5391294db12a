---
description: 
globs: *.cs
alwaysApply: false
---
# .NET Logging Message Templates

## Context

- Apply when writing or reviewing C# code that uses ILogger or similar logging interfaces
- Ensures consistent and efficient logging practices
- Standardizes message template patterns for better analysis and performance

## Critical Rules

- **NEVER** use string interpolation within logging method calls (e.g., `logger.LogWarning($"Error {id}")`)
- **ALWAYS** use message templates with named placeholders (e.g., `logger.LogWarning("Error {Id}", id)`)
- Define message templates as constants in a static class to ensure they don't vary between calls
- Use semantic property names in placeholders that match the parameter meaning (e.g., `{UserId}` not `{id}`)
- Include context in placeholders rather than concatenating strings (e.g., `{TenantId}`, `{RequestPath}`)
- Maintain alphabetical ordering in message template classes for readability

## Examples

<example>
// Good: Using message templates and constants
private static class LogMessages
{
    public const string UserNotFound = "User with ID {UserId} not found";
    public const string RequestProcessed = "Request {RequestId} processed in {ElapsedMs}ms";
}

public void ProcessUser(string userId)
{
    _logger.LogInformation(LogMessages.UserNotFound, userId);
}
</example>

<example type="invalid">
// Bad: Using string interpolation
public void ProcessUser(string userId)
{
    _logger.LogInformation($"User with ID {userId} not found");
}

// Bad: Inline message templates that can vary between calls
public void ProcessUser(string userId)
{
    _logger.LogInformation("User with ID {UserId} not found", userId);
}
</example>

