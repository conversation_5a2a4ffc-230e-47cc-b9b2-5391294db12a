{"permissions": {"allow": ["Bash(dotnet build:*)", "<PERSON><PERSON>(tail:*)", "Bash(ls:*)", "Bash(dotnet ef database drop:*)", "Bash(rm:*)", "Bash(dotnet ef migrations add:*)", "Bash(dotnet ef database update:*)", "<PERSON><PERSON>(findstr:*)", "<PERSON><PERSON>(powershell:*)", "Bash(dotnet test:*)", "Bash(npm run lint)", "Bash(npx tsc:*)", "Bash(node:*)", "Bash(npm run build:*)", "Bash(timeout 120 npm run build)", "Bash(npm run dev:*)", "Bash(timeout 30 npm run dev)", "Bash(npm run lint:*)"], "deny": []}}